"""
TFT+ARIMA Ensemble Model Implementation

This module implements a TFT+ARIMA ensemble model that combines
Temporal Fusion Transformer predictions with ARIMA model predictions.
"""

import logging
import json
import pickle
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, Any, Optional, Union, Tuple

from models.base_model import BaseModel
from models.pytorch_tft_model import PyTorchTFTModel
from models.ensemble_arima_model import EnsembleARIMAModel

logger = logging.getLogger(__name__)

class TFTARIMAEnsembleModel(BaseModel):
    """
    TFT+ARIMA Ensemble Model that combines TFT and ARIMA predictions.
    """
    
    def __init__(self, model_name: str = "tft_arima_ensemble",
                 timeframe: str = "M5", terminal_id: str = "5",
                 config: Optional[Dict[str, Any]] = None, symbol: str = None):
        """
        Initialize TFT+ARIMA ensemble model.

        Args:
            model_name: Name of the model
            timeframe: Trading timeframe
            terminal_id: Terminal ID
            config: Model configuration
            symbol: Trading symbol (optional, can be in config)
        """
        # FIXED: Pass config to BaseModel constructor
        super().__init__(config or {})

        self.model_name = model_name
        self.timeframe = timeframe
        self.terminal_id = terminal_id
        self.config = config or {}

        # Handle symbol parameter
        if symbol:
            self.config['symbol'] = symbol
        
        # Initialize component models
        self.tft_model = None
        self.arima_model = None
        
        # Ensemble weights
        self.tft_weight = self.config.get('tft_weight', 0.6)
        self.arima_weight = self.config.get('arima_weight', 0.4)
        
        # Model paths
        self.model_dir = None
        self.model_path = None
        
        # Metadata
        self.metadata = {}
        self.weights_config = {}

        # Training status
        self.is_trained = False

        logger.info(f"Initialized TFT+ARIMA ensemble model for {timeframe} timeframe")
    
    def build(self) -> None:
        """Build the ensemble model by loading component models."""
        try:
            # Set model directory
            symbol = self.config.get('symbol', 'BTCUSD.a')
            self.model_dir = Path('models') / f"tft_arima_{symbol}_{self.timeframe}"
            self.model_path = self.model_dir

            # CRITICAL FIX: Don't fail if ensemble directory doesn't exist
            # Instead, try to load individual component models
            ensemble_exists = self.model_dir.exists()
            if not ensemble_exists:
                logger.info(f"TFT+ARIMA ensemble directory not found: {self.model_dir}")
                logger.info("Attempting to load individual component models...")
            else:
                logger.info(f"Loading TFT+ARIMA ensemble from: {self.model_dir}")
            
            # Load weights configuration if ensemble exists
            if ensemble_exists:
                weights_file = self.model_dir / "weights.json"
                if weights_file.exists():
                    with open(weights_file, 'r') as f:
                        self.weights_config = json.load(f)
                        self.tft_weight = self.weights_config.get('tft_weight', 0.6)
                        self.arima_weight = self.weights_config.get('arima_weight', 0.4)
                    logger.info(f"Loaded ensemble weights: TFT={self.tft_weight}, ARIMA={self.arima_weight}")

                # Load metadata
                metadata_file = self.model_dir / "metadata.json"
                if metadata_file.exists():
                    with open(metadata_file, 'r') as f:
                        self.metadata = json.load(f)
                    logger.info(f"Loaded ensemble metadata")
            else:
                logger.info("Using default weights: TFT=0.6, ARIMA=0.4")
            
            # Initialize TFT model
            tft_config = self.config.copy()
            tft_config.update(self.metadata.get('tft_config', {}))
            # FIXED: PyTorchTFTModel only accepts config parameter
            self.tft_model = PyTorchTFTModel(config=tft_config)
            
            # Set TFT model path and attributes
            tft_model_dir = Path('models') / f"tft_{symbol}_{self.timeframe}"
            self.tft_model.model_path = tft_model_dir
            self.tft_model.model_dir = tft_model_dir
            # Set additional attributes for compatibility
            self.tft_model.model_name = "tft"
            self.tft_model.timeframe = self.timeframe
            self.tft_model.terminal_id = self.terminal_id
            
            # Load TFT model
            tft_model_file = tft_model_dir / "model.pth"
            if tft_model_file.exists():
                try:
                    self.tft_model.load()
                    logger.info("✅ TFT component model loaded successfully")
                except Exception as tft_load_error:
                    logger.warning(f"Failed to load TFT model: {str(tft_load_error)}")
                    self.tft_model = None
            else:
                logger.warning(f"TFT model not found at {tft_model_file} - ensemble will use ARIMA only")
                self.tft_model = None
            
            # Initialize ARIMA model
            arima_config = self.config.copy()
            arima_config.update(self.metadata.get('arima_config', {}))
            self.arima_model = EnsembleARIMAModel(config=arima_config)
            
            # Set ARIMA model attributes
            self.arima_model.model_name = "arima"
            self.arima_model.terminal_id = self.terminal_id
            self.arima_model.timeframe = self.timeframe
            
            # Build ARIMA model (this will try to load existing models)
            self.arima_model.build()

            # Check if ARIMA model actually has trained models
            arima_has_models = hasattr(self.arima_model, 'models') and len(self.arima_model.models) > 0
            if arima_has_models:
                logger.info("✅ ARIMA component model loaded successfully")
            else:
                logger.warning("⚠️  ARIMA component has no trained models - will use fallback during prediction")

            # Report overall ensemble status
            available_components = []
            if self.tft_model is not None:
                available_components.append("TFT")
            if arima_has_models:
                available_components.append("ARIMA")

            if len(available_components) == 0:
                logger.error("❌ No trained component models available for TFT+ARIMA ensemble")
                logger.info("💡 To fix this, run the training scripts:")
                logger.info("   1. train_all_arima_models.bat (for ARIMA components)")
                logger.info("   2. train_all_tft_models.bat (for TFT components)")
                logger.info("   3. train_all_arima_tft_ensemble.bat (for ensemble training)")
            else:
                logger.info(f"✅ TFT+ARIMA ensemble built with components: {', '.join(available_components)}")

        except Exception as e:
            logger.error(f"Error building TFT+ARIMA ensemble model: {str(e)}")
            # Don't raise the exception - allow the system to continue with fallback
            logger.warning("Continuing with fallback mechanisms...")
            if not hasattr(self, 'tft_model'):
                self.tft_model = None
            if not hasattr(self, 'arima_model'):
                self.arima_model = EnsembleARIMAModel(config=self.config)

    def train(self, X: np.ndarray, y: np.ndarray, **kwargs) -> Dict[str, Any]:
        """
        Train the TFT+ARIMA ensemble model.

        Args:
            X: Input features
            y: Target values
            **kwargs: Additional training parameters

        Returns:
            Training metrics dictionary
        """
        try:
            logger.info("Training TFT+ARIMA ensemble model...")

            # Build the model first if not already built
            if self.tft_model is None or self.arima_model is None:
                self.build()

            # Train TFT component
            tft_metrics = {}
            if self.tft_model is not None:
                try:
                    tft_metrics = self.tft_model.train(X, y, **kwargs)
                    logger.info("✅ TFT component trained successfully")
                except Exception as e:
                    logger.warning(f"TFT component training failed: {str(e)}")

            # Train ARIMA component
            arima_metrics = {}
            if self.arima_model is not None:
                try:
                    # ARIMA expects 1D target data
                    y_1d = y.flatten() if len(y.shape) > 1 else y
                    self.arima_model.train(y_1d)
                    arima_metrics = {"status": "trained"}
                    logger.info("✅ ARIMA component trained successfully")
                except Exception as e:
                    logger.warning(f"ARIMA component training failed: {str(e)}")

            # Mark as trained
            self.is_trained = True

            # Return combined metrics
            training_metrics = {
                "tft_metrics": tft_metrics,
                "arima_metrics": arima_metrics,
                "ensemble_status": "trained",
                "tft_weight": self.tft_weight,
                "arima_weight": self.arima_weight
            }

            logger.info("✅ TFT+ARIMA ensemble training completed")
            return training_metrics

        except Exception as e:
            logger.error(f"Error training TFT+ARIMA ensemble: {str(e)}")
            return {"error": str(e), "ensemble_status": "failed"}

    def save(self, path: Optional[Union[str, Path]] = None) -> None:
        """Save ensemble model configuration."""
        try:
            if path is None:
                path = self.model_path or self.model_dir

            if isinstance(path, str):
                path = Path(path)

            if path is None:
                logger.warning("No save path specified for TFT+ARIMA ensemble")
                return

            path.mkdir(parents=True, exist_ok=True)

            # Save weights
            weights_file = path / "weights.json"
            weights_data = {
                "tft_weight": self.tft_weight,
                "arima_weight": self.arima_weight,
                "ensemble_type": "tft_arima",
                "created_at": pd.Timestamp.now().isoformat(),
                "model_version": "1.0.0"
            }

            with open(weights_file, 'w') as f:
                json.dump(weights_data, f, indent=4)

            # Save metadata
            metadata_file = path / "metadata.json"
            metadata = {
                "model_name": self.model_name,
                "timeframe": self.timeframe,
                "terminal_id": self.terminal_id,
                "tft_weight": self.tft_weight,
                "arima_weight": self.arima_weight,
                "ensemble_status": "trained" if self.is_trained else "not_trained",
                "created_at": pd.Timestamp.now().isoformat()
            }

            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=4)

            logger.info(f"Saved TFT+ARIMA ensemble configuration to {path}")

        except Exception as e:
            logger.error(f"Error saving TFT+ARIMA ensemble: {str(e)}")
    
    def predict(self, data: Union[pd.DataFrame, np.ndarray]) -> np.ndarray:
        """
        Make ensemble predictions using both TFT and ARIMA models.

        Args:
            data: Input data for prediction

        Returns:
            Ensemble predictions
        """
        try:
            # FIXED: Check if models are available and built
            if self.tft_model is None or self.arima_model is None:
                logger.warning("Ensemble models not built, attempting to build...")
                self.build()

            # If still not available, return empty array
            if self.tft_model is None and self.arima_model is None:
                logger.error("No component models available for prediction")
                return np.array([])

            # Check if we have at least one working model
            if self.tft_model is None:
                logger.warning("TFT model not available, using ARIMA only")
            if self.arima_model is None:
                logger.warning("ARIMA model not available, using TFT only")
            predictions = []
            weights = []
            
            # Get TFT prediction
            if self.tft_model is not None:
                try:
                    tft_pred = self.tft_model.predict(data)
                    if tft_pred is not None and not np.isnan(tft_pred).any():
                        predictions.append(tft_pred)
                        weights.append(self.tft_weight)
                        logger.debug(f"TFT prediction: {tft_pred.shape if hasattr(tft_pred, 'shape') else type(tft_pred)}")
                except Exception as e:
                    logger.warning(f"TFT prediction failed: {str(e)}")
            
            # Get ARIMA prediction
            if self.arima_model is not None:
                try:
                    # FIXED: ARIMA model expects different parameters than TFT
                    # EnsembleARIMAModel.predict expects (n_periods, exog) not (data)
                    if hasattr(data, 'shape') and len(data.shape) >= 2:
                        n_periods = data.shape[0]  # Use batch size as n_periods
                        # Extract exogenous variables if available (skip first column which might be target)
                        exog = data[:, 1:] if data.shape[1] > 1 else None
                    else:
                        n_periods = 1  # Default for single prediction
                        exog = None

                    arima_pred = self.arima_model.predict(n_periods=n_periods, exog=exog)
                    if arima_pred is not None and not np.isnan(arima_pred).any():
                        predictions.append(arima_pred)
                        weights.append(self.arima_weight)
                        logger.debug(f"ARIMA prediction: {arima_pred.shape if hasattr(arima_pred, 'shape') else type(arima_pred)}")
                except Exception as e:
                    logger.warning(f"ARIMA prediction failed: {str(e)}")
                    # Try fallback with minimal parameters
                    try:
                        arima_pred = self.arima_model.predict(n_periods=1)
                        if arima_pred is not None and not np.isnan(arima_pred).any():
                            predictions.append(arima_pred)
                            weights.append(self.arima_weight)
                            logger.debug(f"ARIMA fallback prediction: {arima_pred.shape if hasattr(arima_pred, 'shape') else type(arima_pred)}")
                    except Exception as fallback_error:
                        logger.warning(f"ARIMA fallback prediction also failed: {str(fallback_error)}")
            
            # Combine predictions
            if len(predictions) == 0:
                logger.error("No valid predictions from component models")
                return np.array([0.0])
            elif len(predictions) == 1:
                logger.warning("Only one component model provided prediction")
                return predictions[0]
            else:
                # Normalize weights
                total_weight = sum(weights)
                if total_weight > 0:
                    weights = [w / total_weight for w in weights]
                else:
                    weights = [1.0 / len(weights)] * len(weights)
                
                # Weighted average
                ensemble_pred = sum(pred * weight for pred, weight in zip(predictions, weights))
                logger.debug(f"Ensemble prediction: {ensemble_pred.shape if hasattr(ensemble_pred, 'shape') else type(ensemble_pred)}")
                return ensemble_pred
                
        except Exception as e:
            logger.error(f"Error in TFT+ARIMA ensemble prediction: {str(e)}")
            return np.array([0.0])
    

    
    def load(self, path: Optional[Union[str, Path]] = None) -> None:
        """Load ensemble model configuration."""
        try:
            if path is None:
                path = self.model_path or self.model_dir
            
            if isinstance(path, str):
                path = Path(path)
            
            if not path.exists():
                logger.warning(f"TFT+ARIMA ensemble path does not exist: {path}")
                return
            
            # Load weights
            weights_file = path / "weights.json"
            if weights_file.exists():
                with open(weights_file, 'r') as f:
                    weights_data = json.load(f)
                    self.tft_weight = weights_data.get('tft_weight', 0.6)
                    self.arima_weight = weights_data.get('arima_weight', 0.4)
                    self.weights_config = weights_data
                logger.info(f"Loaded TFT+ARIMA ensemble weights from {weights_file}")
            
            # Load metadata
            metadata_file = path / "metadata.json"
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    self.metadata = json.load(f)
                logger.info(f"Loaded TFT+ARIMA ensemble metadata from {metadata_file}")
            
            # Build component models
            self.build()
            
        except Exception as e:
            logger.error(f"Error loading TFT+ARIMA ensemble model: {str(e)}")
            raise

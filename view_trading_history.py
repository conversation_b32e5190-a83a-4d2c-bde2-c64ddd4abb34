#!/usr/bin/env python3
"""
MT5 Trading History Viewer

This script allows you to view trading history from all MT5 terminals
WITHOUT disabling Algo Trading. It connects to each terminal safely
and retrieves historical data.

Usage:
    python view_trading_history.py --terminal 1 --days 7
    python view_trading_history.py --all --days 1
    python view_trading_history.py --terminal 3 --symbol BTCUSD.a
"""

import MetaTrader5 as mt5
import pandas as pd
import argparse
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_terminal_config():
    """Load terminal configurations from config.json"""
    try:
        with open('config/config.json', 'r') as f:
            config = json.load(f)
        return config['mt5']['terminals']
    except Exception as e:
        logger.error(f"Failed to load config: {e}")
        return {}

def connect_to_terminal(terminal_id: str, terminal_config: Dict) -> bool:
    """
    Connect to MT5 terminal safely without disabling Algo Trading.
    
    Args:
        terminal_id: Terminal ID (1, 2, 3, 4, 5)
        terminal_config: Terminal configuration
        
    Returns:
        bool: True if connected successfully
    """
    try:
        logger.info(f"Connecting to Terminal {terminal_id}...")
        
        # Use portable=True to preserve Algo Trading
        success = mt5.initialize(
            path=terminal_config["path"],
            portable=True  # CRITICAL: Preserves Algo Trading
        )
        
        if not success:
            error = mt5.last_error()
            logger.error(f"Failed to connect to Terminal {terminal_id}: {error}")
            return False
            
        # Verify connection
        terminal_info = mt5.terminal_info()
        if not terminal_info:
            logger.error(f"Failed to get terminal info for Terminal {terminal_id}")
            return False
            
        account_info = mt5.account_info()
        if not account_info:
            logger.error(f"Failed to get account info for Terminal {terminal_id}")
            return False
            
        logger.info(f"✅ Connected to Terminal {terminal_id}")
        logger.info(f"   Account: {account_info.login}")
        logger.info(f"   Server: {account_info.server}")
        logger.info(f"   Balance: ${account_info.balance:,.2f}")
        logger.info(f"   Algo Trading: {'✅ ENABLED' if terminal_info.trade_allowed else '❌ DISABLED'}")
        
        return True
        
    except Exception as e:
        logger.error(f"Exception connecting to Terminal {terminal_id}: {e}")
        return False

def get_trading_history(symbol: Optional[str] = None, days: int = 7) -> Dict[str, Any]:
    """
    Get comprehensive trading history without affecting Algo Trading.
    
    Args:
        symbol: Optional symbol filter (e.g., "BTCUSD.a")
        days: Number of days to look back
        
    Returns:
        Dict containing all trading history data
    """
    from_date = datetime.now() - timedelta(days=days)
    to_date = datetime.now()
    
    history_data = {
        'positions': [],
        'orders': [],
        'history_orders': [],
        'history_deals': [],
        'summary': {}
    }
    
    try:
        # 1. Get current positions
        if symbol:
            positions = mt5.positions_get(symbol=symbol)
        else:
            positions = mt5.positions_get()
            
        if positions:
            history_data['positions'] = [dict(pos._asdict()) for pos in positions]
            logger.info(f"📊 Found {len(positions)} open positions")
        
        # 2. Get pending orders
        if symbol:
            orders = mt5.orders_get(symbol=symbol)
        else:
            orders = mt5.orders_get()
            
        if orders:
            history_data['orders'] = [dict(order._asdict()) for order in orders]
            logger.info(f"📋 Found {len(orders)} pending orders")
        
        # 3. Get historical orders
        if symbol:
            hist_orders = mt5.history_orders_get(from_date, to_date, symbol=symbol)
        else:
            hist_orders = mt5.history_orders_get(from_date, to_date)
            
        if hist_orders:
            history_data['history_orders'] = [dict(order._asdict()) for order in hist_orders]
            logger.info(f"📈 Found {len(hist_orders)} historical orders")
        
        # 4. Get historical deals
        if symbol:
            hist_deals = mt5.history_deals_get(from_date, to_date, symbol=symbol)
        else:
            hist_deals = mt5.history_deals_get(from_date, to_date)
            
        if hist_deals:
            history_data['history_deals'] = [dict(deal._asdict()) for deal in hist_deals]
            logger.info(f"💰 Found {len(hist_deals)} historical deals")
            
            # Calculate summary statistics
            total_profit = sum(deal.get('profit', 0) for deal in history_data['history_deals'])
            total_volume = sum(deal.get('volume', 0) for deal in history_data['history_deals'])
            
            history_data['summary'] = {
                'total_deals': len(hist_deals),
                'total_profit': total_profit,
                'total_volume': total_volume,
                'period_days': days,
                'symbol_filter': symbol
            }
            
            logger.info(f"💵 Total P&L: ${total_profit:,.2f}")
            logger.info(f"📊 Total Volume: {total_volume:,.2f} lots")
        
        return history_data
        
    except Exception as e:
        logger.error(f"Error retrieving trading history: {e}")
        return history_data

def display_trading_summary(history_data: Dict[str, Any], terminal_id: str):
    """Display a formatted summary of trading history."""
    
    print(f"\n{'='*60}")
    print(f"📊 TRADING HISTORY SUMMARY - TERMINAL {terminal_id}")
    print(f"{'='*60}")
    
    summary = history_data.get('summary', {})
    if summary:
        print(f"Period: {summary.get('period_days', 0)} days")
        if summary.get('symbol_filter'):
            print(f"Symbol: {summary['symbol_filter']}")
        print(f"Total Deals: {summary.get('total_deals', 0)}")
        print(f"Total P&L: ${summary.get('total_profit', 0):,.2f}")
        print(f"Total Volume: {summary.get('total_volume', 0):,.2f} lots")
    
    print(f"\n📊 Current Status:")
    print(f"Open Positions: {len(history_data.get('positions', []))}")
    print(f"Pending Orders: {len(history_data.get('orders', []))}")
    print(f"Historical Orders: {len(history_data.get('history_orders', []))}")
    print(f"Historical Deals: {len(history_data.get('history_deals', []))}")
    
    # Show recent deals
    recent_deals = history_data.get('history_deals', [])[-10:]  # Last 10 deals
    if recent_deals:
        print(f"\n💰 Recent Deals (Last 10):")
        print(f"{'Time':<20} {'Type':<6} {'Volume':<8} {'Price':<12} {'Profit':<10}")
        print(f"{'-'*60}")
        
        for deal in recent_deals:
            deal_time = datetime.fromtimestamp(deal.get('time', 0)).strftime('%Y-%m-%d %H:%M:%S')
            deal_type = 'BUY' if deal.get('type', 0) == 0 else 'SELL'
            volume = deal.get('volume', 0)
            price = deal.get('price', 0)
            profit = deal.get('profit', 0)
            
            print(f"{deal_time:<20} {deal_type:<6} {volume:<8.2f} {price:<12.5f} ${profit:<9.2f}")

def main():
    """Main function to view trading history."""
    parser = argparse.ArgumentParser(description='View MT5 Trading History')
    parser.add_argument('--terminal', type=int, choices=[1, 2, 3, 4, 5], 
                       help='Terminal ID to check (1-5)')
    parser.add_argument('--all', action='store_true', 
                       help='Check all terminals')
    parser.add_argument('--symbol', type=str, 
                       help='Symbol to filter (e.g., BTCUSD.a)')
    parser.add_argument('--days', type=int, default=7, 
                       help='Number of days to look back (default: 7)')
    
    args = parser.parse_args()
    
    if not args.terminal and not args.all:
        print("Please specify --terminal <ID> or --all")
        return
    
    # Load terminal configurations
    terminals = load_terminal_config()
    if not terminals:
        logger.error("No terminal configurations found!")
        return
    
    # Determine which terminals to check
    if args.all:
        terminal_ids = list(terminals.keys())
    else:
        terminal_ids = [str(args.terminal)]
    
    logger.info(f"🔍 Checking trading history for {len(terminal_ids)} terminal(s)")
    logger.info(f"📅 Period: {args.days} days")
    if args.symbol:
        logger.info(f"🎯 Symbol filter: {args.symbol}")
    
    # Check each terminal
    for terminal_id in terminal_ids:
        if terminal_id not in terminals:
            logger.error(f"Terminal {terminal_id} not found in config")
            continue
            
        terminal_config = terminals[terminal_id]
        
        # Connect to terminal
        if connect_to_terminal(terminal_id, terminal_config):
            try:
                # Get trading history
                history_data = get_trading_history(args.symbol, args.days)
                
                # Display summary
                display_trading_summary(history_data, terminal_id)
                
            except Exception as e:
                logger.error(f"Error processing Terminal {terminal_id}: {e}")
            
            # Note: We don't call mt5.shutdown() to preserve Algo Trading
            logger.info(f"✅ Finished checking Terminal {terminal_id} (Algo Trading preserved)")
        
        print()  # Add spacing between terminals

if __name__ == "__main__":
    main()

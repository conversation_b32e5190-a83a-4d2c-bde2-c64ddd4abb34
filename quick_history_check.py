#!/usr/bin/env python3
"""
Quick MT5 Trading History Check

Simple script to quickly check trading history from all terminals
without disabling Algo Trading.

Usage:
    python quick_history_check.py
"""

import MetaTrader5 as mt5
import json
from datetime import datetime, timedelta
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_config():
    """Load terminal configurations."""
    try:
        with open('config/config.json', 'r') as f:
            config = json.load(f)
        return config['mt5']['terminals']
    except Exception as e:
        logger.error(f"Failed to load config: {e}")
        return {}

def quick_check_terminal(terminal_id: str, terminal_config: dict):
    """Quick check of terminal trading history."""
    
    print(f"\n{'='*50}")
    print(f"🔍 TERMINAL {terminal_id} QUICK CHECK")
    print(f"{'='*50}")
    
    try:
        # Connect with portable=True to preserve Algo Trading
        success = mt5.initialize(
            path=terminal_config["path"],
            portable=True
        )
        
        if not success:
            print(f"❌ Failed to connect: {mt5.last_error()}")
            return
        
        # Get account info
        account_info = mt5.account_info()
        terminal_info = mt5.terminal_info()
        
        if account_info and terminal_info:
            print(f"✅ Connected to Account: {account_info.login}")
            print(f"   Server: {account_info.server}")
            print(f"   Balance: ${account_info.balance:,.2f}")
            print(f"   Algo Trading: {'✅ ENABLED' if terminal_info.trade_allowed else '❌ DISABLED'}")
        
        # Get current positions
        positions = mt5.positions_get()
        print(f"📊 Open Positions: {len(positions) if positions else 0}")
        
        # Get recent deals (last 24 hours)
        from_date = datetime.now() - timedelta(hours=24)
        deals = mt5.history_deals_get(from_date)
        
        if deals and len(deals) > 0:
            print(f"💰 Recent Deals (24h): {len(deals)}")
            
            # Calculate total P&L
            total_profit = sum(deal.profit for deal in deals)
            print(f"💵 Total P&L (24h): ${total_profit:,.2f}")
            
            # Show last 5 deals
            print(f"\n📋 Last 5 Deals:")
            print(f"{'Time':<20} {'Type':<6} {'Volume':<8} {'Profit':<10}")
            print(f"{'-'*45}")
            
            for deal in deals[-5:]:
                deal_time = datetime.fromtimestamp(deal.time).strftime('%H:%M:%S')
                deal_type = 'BUY' if deal.type == 0 else 'SELL'
                print(f"{deal_time:<20} {deal_type:<6} {deal.volume:<8.2f} ${deal.profit:<9.2f}")
        else:
            print(f"💰 Recent Deals (24h): 0")
            print(f"💵 Total P&L (24h): $0.00")
        
        # Get recent orders (last 24 hours)
        orders = mt5.history_orders_get(from_date)
        print(f"📈 Recent Orders (24h): {len(orders) if orders else 0}")
        
    except Exception as e:
        print(f"❌ Error checking Terminal {terminal_id}: {e}")

def main():
    """Main function for quick history check."""
    
    print("🚀 QUICK MT5 TRADING HISTORY CHECK")
    print("=" * 60)
    print("This script checks trading history WITHOUT disabling Algo Trading")
    print("=" * 60)
    
    # Load terminal configurations
    terminals = load_config()
    if not terminals:
        print("❌ No terminal configurations found!")
        return
    
    # Check each terminal
    for terminal_id, terminal_config in terminals.items():
        quick_check_terminal(terminal_id, terminal_config)
    
    print(f"\n{'='*60}")
    print("✅ QUICK CHECK COMPLETED")
    print("🔒 Algo Trading status preserved on all terminals")
    print("=" * 60)

if __name__ == "__main__":
    main()

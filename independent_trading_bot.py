"""
Independent Trading Bot with Proportional Ensemble Allocation

This module implements a trading bot system where each terminal operates independently
with its own dedicated model, but uses an ensemble approach internally with different
model weights. Each terminal is allocated a proportion of the total capital/risk.

Key features:
1. Each terminal operates independently with its dedicated primary model
2. Each terminal uses all models but with different weights (primary model gets highest weight)
3. Capital/risk is allocated proportionally across terminals
4. Allocations can be adjusted based on performance
"""

import os
import time
import logging
import concurrent.futures
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field
import threading

# Import MT5 connector
from multi_mt5_connection import MT5MultiConnector

# Import configuration manager
from config.unified_config import (
    config_manager,
    get_config,
    get_mt5_config,
    get_strategy_config,
    get_model_config
)

# Import model manager
from utils.model_manager import ModelManager

# Import signal generator
from trading.signal_generator import SignalGenerator, TradingSignal

# TradeOrderV2 class is defined in this file

# Import error handler
from utils.error_handler import ErrorHandler

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Create default error handler
default_error_handler = ErrorHandler()


# TradeOrder class has been replaced by TradeOrderV2, but we keep it for backward compatibility

class TradeOrder:
    """Legacy class representing a trade order."""
    def __init__(self, ticket, action=None, symbol=None, confidence=0.0, timestamp=None, source_model='unknown',
                 price=None, volume=None, stop_loss=None, take_profit=None, prediction=None,
                 expected_return=None, volatility=None, market_regime=None, signal_strength=None,
                 model_predictions=None, metadata=None, status='open',
                 close_time=None, close_price=None, profit=0.0, profit_loss=0.0):
        # Basic trade information
        self.ticket = ticket
        self.action = action
        self.symbol = symbol
        self.confidence = float(confidence) if confidence is not None else 0.0
        self.timestamp = timestamp if timestamp is not None else datetime.now()
        self.source_model = source_model

        # Trade parameters
        self.price = price
        self.volume = volume
        self.stop_loss = stop_loss
        self.take_profit = take_profit

        # Model information
        self.prediction = prediction
        self.expected_return = expected_return
        self.volatility = volatility
        self.market_regime = market_regime
        self.signal_strength = signal_strength
        self.model_predictions = {} if model_predictions is None else model_predictions
        self.metadata = {} if metadata is None else metadata

        # Trade status and results
        self.status = status
        self.close_time = close_time
        self.close_price = close_price

        # Add profit attributes for compatibility with TradeOrderV2
        self.profit = profit
        self.profit_loss = profit_loss


class TradeOrderV2:
    """New class representing a trade order with profit information."""
    def __init__(self, ticket, action=None, symbol=None, confidence=0.0, timestamp=None, source_model='unknown',
                 price=None, volume=None, stop_loss=None, take_profit=None, prediction=None,
                 expected_return=None, volatility=None, market_regime=None, signal_strength=None,
                 model_predictions=None, metadata=None, profit=0.0, profit_loss=0.0, status='open',
                 close_time=None, close_price=None):
        # Basic trade information
        self.ticket = ticket
        self.action = action
        self.symbol = symbol
        self.confidence = float(confidence) if confidence is not None else 0.0
        self.timestamp = timestamp if timestamp is not None else datetime.now()
        self.source_model = source_model

        # Trade parameters
        self.price = price
        self.volume = volume
        self.stop_loss = stop_loss
        self.take_profit = take_profit

        # Model information
        self.prediction = prediction
        self.expected_return = expected_return
        self.volatility = volatility
        self.market_regime = market_regime
        self.signal_strength = signal_strength
        self.model_predictions = {} if model_predictions is None else model_predictions
        self.metadata = {} if metadata is None else metadata

        # Trade status and results
        self.status = status
        self.close_time = close_time
        self.close_price = close_price

        # Profit information - directly set as instance attributes
        # Ensure profit is always a float and never None
        self.profit = 0.0 if profit is None else float(profit)
        self.profit_loss = self.profit if profit_loss is None else float(profit_loss)

    @classmethod
    def from_signal(cls, signal, ticket):
        """Create a TradeOrder from a signal and ticket."""
        return cls(
            ticket=ticket,
            action=getattr(signal, 'action', None),
            symbol=getattr(signal, 'symbol', None),
            confidence=getattr(signal, 'confidence', 0.0),
            timestamp=getattr(signal, 'timestamp', datetime.now()),
            source_model=getattr(signal, 'source_model', 'unknown'),
            price=getattr(signal, 'price', None),
            volume=getattr(signal, 'volume', None),
            stop_loss=getattr(signal, 'stop_loss', None),
            take_profit=getattr(signal, 'take_profit', None),
            prediction=getattr(signal, 'prediction', None),
            expected_return=getattr(signal, 'expected_return', None),
            volatility=getattr(signal, 'volatility', None),
            market_regime=getattr(signal, 'market_regime', None),
            signal_strength=getattr(signal, 'signal_strength', None),
            model_predictions=getattr(signal, 'model_predictions', None),
            metadata=getattr(signal, 'metadata', None),
            profit=getattr(signal, 'profit', 0.0) or 0.0,
            profit_loss=getattr(signal, 'profit_loss', 0.0) or 0.0
        )

    def __str__(self):
        return f"TradeOrderV2(action={self.action}, symbol={self.symbol}, confidence={self.confidence:.4f}, profit={self.profit:.2f})"


@dataclass
class TerminalConfig:
    """Configuration for a terminal with its model allocation."""
    terminal_id: int
    primary_model: str
    allocation_percentage: float
    model_weights: Dict[str, float] = None

    def __post_init__(self):
        """Initialize model weights if not provided."""
        if self.model_weights is None:
            # Default weights: primary model gets 60%, others share the rest
            self.model_weights = {
                "lstm": 0.2,
                "tft": 0.2,
                "arima": 0.2
            }
            # Override with primary model weight
            self.model_weights[self.primary_model] = 0.6


class IndependentTradingBot:
    """Trading bot that operates independently on a specific terminal."""

    def __init__(self, terminal_config: TerminalConfig):
        """
        Initialize the independent trading bot.

        Args:
            terminal_config: Configuration for this terminal
        """
        self.terminal_config = terminal_config
        self.terminal_id = terminal_config.terminal_id
        self.primary_model = terminal_config.primary_model
        self.allocation_percentage = terminal_config.allocation_percentage
        self.model_weights = terminal_config.model_weights

        # Initialize MT5 connector
        self.mt5_connector = MT5MultiConnector()

        # Get trading parameters from configuration
        strategy_config = get_strategy_config()
        self.symbol = strategy_config.symbol
        self.timeframe = strategy_config.timeframes[0]  # Primary timeframe
        self.max_positions = strategy_config.max_positions
        self.base_lot_size = strategy_config.lot_size
        self.stop_loss_pips = strategy_config.stop_loss_pips
        self.take_profit_pips = strategy_config.take_profit_pips
        self.risk_per_trade = strategy_config.risk_per_trade

        # Initialize model manager with all models, but primary model gets highest weight
        self.model_manager = self._initialize_model_manager()

        # Initialize signal generator that uses all models with custom weights
        self.signal_generator = self._initialize_signal_generator()

        # Performance tracking
        self.trades = []  # Initialize with empty list - no trades loaded from disk

        # Reset performance metrics to ensure clean start
        self.performance_metrics = {
            "total_trades": 0,
            "winning_trades": 0,
            "losing_trades": 0,
            "total_profit": 0.0,
            "win_rate": 0.0,
            "profit_factor": 0.0,
            "average_win": 0.0,
            "average_loss": 0.0,
            "max_drawdown": 0.0,
            "sharpe_ratio": 0.0
        }

        logger.info(f"Initialized IndependentTradingBot for Terminal {self.terminal_id} with primary model {self.primary_model}")
        logger.info(f"Allocation: {self.allocation_percentage}%, Model weights: {self.model_weights}")

    def _initialize_model_manager(self):
        """Initialize model manager with all models but primary model gets highest weight."""
        model_manager = ModelManager(
            terminal_id=self.terminal_id,
            timeframe=self.timeframe,
            config_manager=config_manager,
            error_handler=default_error_handler
        )

        # Load all models
        model_manager.load_all_models()

        # Set custom weights
        model_manager.set_model_weights(self.model_weights)

        return model_manager

    def _initialize_signal_generator(self):
        """Initialize signal generator with custom model weights."""
        return SignalGenerator(
            terminal_id=self.terminal_id,
            timeframe=self.timeframe,
            model_manager=self.model_manager,
            config_manager=config_manager,
            error_handler=default_error_handler
        )

    def connect(self):
        """Connect to the dedicated terminal."""
        return self.mt5_connector.connect(self.terminal_id)

    def calculate_lot_size(self):
        """Calculate lot size based on allocation percentage."""
        # Get account equity
        if not self.mt5_connector.is_connected():
            self.connect()

        account_info = self.mt5_connector.get_account_info()
        if not account_info:
            return self.base_lot_size

        equity = account_info.get("equity", 10000)  # Default to 10000 if not available

        # Calculate lot size based on allocation percentage and risk per trade
        allocated_equity = equity * (self.allocation_percentage / 100)
        risk_amount = allocated_equity * self.risk_per_trade

        # Get symbol info for calculating lot size
        symbol_info = self.mt5_connector.get_symbol_info(self.symbol)
        if not symbol_info:
            return self.base_lot_size

        # Calculate lot size based on risk amount and stop loss
        point_value = symbol_info.get("point_value", 0.1)  # Value of 1 point in account currency

        if point_value > 0 and self.stop_loss_pips > 0:
            lot_size = risk_amount / (self.stop_loss_pips * point_value)
            # Round to nearest lot step
            lot_step = symbol_info.get("volume_step", 0.01)
            lot_size = round(lot_size / lot_step) * lot_step

            # Ensure within min/max limits
            lot_min = symbol_info.get("volume_min", 0.01)
            lot_max = symbol_info.get("volume_max", 100.0)
            lot_size = max(lot_min, min(lot_size, lot_max))

            return lot_size

        return self.base_lot_size

    def generate_trading_signal(self):
        """Generate trading signal using all models with custom weights."""
        # Get market data
        if not self.mt5_connector.is_connected():
            self.connect()

        data = self.mt5_connector.get_historical_data(self.symbol, self.timeframe, 500)
        if data is None:
            return None

        # Preprocess data for models
        X = self.signal_generator.preprocess_data(data)

        # Generate signal
        signal = self.signal_generator.generate_signal(X)

        return signal

    def execute_trade(self, signal):
        """Execute trade based on signal."""
        if not signal or not hasattr(signal, 'action') or signal.action == 'hold':
            return None

        # CRITICAL FIX: Check minimum confidence threshold
        min_confidence = 0.6  # Only trade signals with >60% confidence
        signal_confidence = getattr(signal, 'confidence', 0.0)
        if signal_confidence < min_confidence:
            logger.info(f"[{self.terminal_id}] Trade blocked: Low confidence {signal_confidence:.3f} < {min_confidence}")
            return None

        # CRITICAL FIX: Check if trading is disabled due to position limits
        if hasattr(self, 'trading_enabled') and not self.trading_enabled:
            logger.warning(f"[{self.terminal_id}] Trade execution blocked: Trading disabled due to position limits")
            return None

        # Calculate lot size based on allocation percentage
        lot_size = self.calculate_lot_size()

        # Check if we already have positions
        positions = self.mt5_connector.get_positions(self.symbol)
        if len(positions) >= self.max_positions:
            logger.warning(f"[{self.terminal_id}] Trade execution blocked: {len(positions)} positions >= max {self.max_positions}")
            return None

        # Get stop loss and take profit values with safe defaults
        stop_loss = getattr(signal, 'stop_loss', None)
        take_profit = getattr(signal, 'take_profit', None)

        # Execute trade
        if signal.action == 'buy':
            result = self.mt5_connector.place_order(
                self.symbol,
                "BUY",
                lot_size,
                stop_loss=stop_loss,
                take_profit=take_profit,
                comment=f"Bot {self.terminal_id} {self.primary_model} signal"
            )
        elif signal.action == 'sell':
            result = self.mt5_connector.place_order(
                self.symbol,
                "SELL",
                lot_size,
                stop_loss=stop_loss,
                take_profit=take_profit,
                comment=f"Bot {self.terminal_id} {self.primary_model} signal"
            )
        else:
            return None

        # Track trade for performance monitoring
        if result:
            # Create TradeOrderV2 object
            trade = TradeOrderV2(
                ticket=result,
                action=getattr(signal, 'action', 'unknown'),
                symbol=self.symbol,
                price=getattr(signal, 'price', None),
                stop_loss=stop_loss,
                take_profit=take_profit,
                volume=lot_size,
                timestamp=datetime.now(),
                source_model=self.primary_model,
                profit=0.0,
                profit_loss=0.0,
                status="open"
            )
            self.trades.append(trade)

            logger.info(f"Terminal {self.terminal_id} executed {getattr(signal, 'action', 'unknown')} order: Ticket {result}, Lot Size {lot_size}")

        return result

    def update_trades(self):
        """Update status of open trades."""
        try:
            if not self.mt5_connector.is_connected():
                self.connect()

            # Get current positions
            positions = self.mt5_connector.get_positions(self.symbol)
            position_tickets = [p["ticket"] for p in positions] if positions else []

            # Get history orders for closed positions
            history = self.mt5_connector.get_history_orders(self.symbol)
            history_dict = {h["ticket"]: h for h in history} if history else {}

            # Create a new list for trades to ensure we only have TradeOrderV2 objects
            new_trades = []

            # Check if self.trades is None or empty
            if not self.trades:
                logger.info(f"No trades to update for terminal {self.terminal_id}")
                return

            # Update trades
            for i, trade in enumerate(self.trades):
                # Check if this is a TradeOrder or TradeOrderV2 object
                if isinstance(trade, dict):
                    # Handle dictionary case
                    logger.info(f"Converting dictionary trade to TradeOrderV2")
                    new_trade = TradeOrderV2(
                        ticket=trade.get('ticket', 0),
                        action=trade.get('action'),
                        symbol=trade.get('symbol', self.symbol),
                        confidence=trade.get('confidence', 0.0),
                        timestamp=trade.get('timestamp', datetime.now()),
                        source_model=trade.get('source_model', 'unknown'),
                        price=trade.get('price'),
                        volume=trade.get('volume'),
                        stop_loss=trade.get('stop_loss'),
                        take_profit=trade.get('take_profit'),
                        status=trade.get('status', 'open'),
                        close_time=trade.get('close_time'),
                        close_price=trade.get('close_price'),
                        profit=trade.get('profit', 0.0),
                        profit_loss=trade.get('profit_loss', 0.0)
                    )
                    # Replace the original trade with the new one
                    self.trades[i] = new_trade
                    trade = new_trade
                    logger.info(f"Converted dictionary to TradeOrderV2")
                elif isinstance(trade, TradeOrder):
                    # Handle old TradeOrder object
                    logger.info(f"Converting TradeOrder object to TradeOrderV2")
                    try:
                        # Create a new TradeOrderV2 with the same attributes
                        # Ensure profit and profit_loss have default values
                        profit_value = 0.0
                        if hasattr(trade, 'profit'):
                            profit_value = trade.profit if trade.profit is not None else 0.0

                        profit_loss_value = 0.0
                        if hasattr(trade, 'profit_loss'):
                            profit_loss_value = trade.profit_loss if trade.profit_loss is not None else 0.0

                        new_trade = TradeOrderV2(
                            ticket=trade.ticket,
                            action=trade.action,
                            symbol=trade.symbol if hasattr(trade, 'symbol') else self.symbol,
                            confidence=trade.confidence if hasattr(trade, 'confidence') else 0.0,
                            timestamp=trade.timestamp if hasattr(trade, 'timestamp') else datetime.now(),
                            source_model=trade.source_model if hasattr(trade, 'source_model') else 'unknown',
                            price=trade.price if hasattr(trade, 'price') else None,
                            volume=trade.volume if hasattr(trade, 'volume') else None,
                            stop_loss=trade.stop_loss if hasattr(trade, 'stop_loss') else None,
                            take_profit=trade.take_profit if hasattr(trade, 'take_profit') else None,
                            status=trade.status if hasattr(trade, 'status') else 'open',
                            close_time=trade.close_time if hasattr(trade, 'close_time') else None,
                            close_price=trade.close_price if hasattr(trade, 'close_price') else None,
                            profit=profit_value,
                            profit_loss=profit_loss_value
                        )
                        # Replace the original trade with the new one
                        self.trades[i] = new_trade
                        trade = new_trade
                        logger.info(f"Converted TradeOrder to TradeOrderV2 for ticket {trade.ticket}")
                    except Exception as conv_error:
                        logger.error(f"Error converting TradeOrder to TradeOrderV2: {str(conv_error)}")
                        # Skip this trade if conversion fails
                        continue
                elif not isinstance(trade, TradeOrderV2):
                    # Handle any other type
                    logger.info(f"Converting unknown object to TradeOrderV2")
                    try:
                        # Create a new TradeOrderV2 with the same attributes
                        new_trade = TradeOrderV2(
                            ticket=getattr(trade, 'ticket', 0),
                            action=getattr(trade, 'action', None),
                            symbol=getattr(trade, 'symbol', self.symbol),
                            confidence=getattr(trade, 'confidence', 0.0),
                            timestamp=getattr(trade, 'timestamp', datetime.now()),
                            source_model=getattr(trade, 'source_model', 'unknown'),
                            price=getattr(trade, 'price', None),
                            volume=getattr(trade, 'volume', None),
                            stop_loss=getattr(trade, 'stop_loss', None),
                            take_profit=getattr(trade, 'take_profit', None),
                            status=getattr(trade, 'status', 'open'),
                            close_time=getattr(trade, 'close_time', None),
                            close_price=getattr(trade, 'close_price', None),
                            profit=getattr(trade, 'profit', 0.0),
                            profit_loss=getattr(trade, 'profit_loss', 0.0)
                        )
                        # Replace the original trade with the new one
                        self.trades[i] = new_trade
                        trade = new_trade
                        logger.info(f"Converted unknown object to TradeOrderV2 for ticket {trade.ticket}")
                    except Exception as conv_error:
                        logger.error(f"Error converting unknown object to TradeOrderV2: {str(conv_error)}")
                        # Skip this trade if conversion fails
                        continue

                # Add the trade to our new list
                new_trades.append(trade)

                # Check if the trade is open
                if trade.status == "open":
                    if trade.ticket not in position_tickets:
                        # Trade is closed
                        if trade.ticket in history_dict:
                            history_order = history_dict[trade.ticket]
                            trade.close_time = history_order.get("close_time", datetime.now())
                            trade.close_price = history_order.get("close_price", 0)

                            # Ensure profit values are properly set with safe defaults
                            profit = history_order.get("profit", 0.0)
                            if profit is None:
                                profit = 0.0

                            # Set profit attribute directly
                            trade.profit = float(profit)
                            trade.profit_loss = float(profit)  # Update profit_loss field for compatibility
                            trade.status = "closed"

                            # Update performance metrics
                            self.performance_metrics["total_trades"] += 1
                            if profit > 0:
                                self.performance_metrics["winning_trades"] += 1
                            else:
                                self.performance_metrics["losing_trades"] += 1
                            self.performance_metrics["total_profit"] += profit

                            logger.info(f"Terminal {self.terminal_id} closed trade: Ticket {trade.ticket}, Profit {profit}")

            # Replace the old trades list with our new one
            self.trades = new_trades

        except Exception as e:
            logger.error(f"Error updating trades for terminal {self.terminal_id}: {str(e)}")
            logger.debug(f"Exception details for update_trades:", exc_info=True)

    def calculate_performance_metrics(self):
        """Calculate performance metrics based on trade history."""
        # Do NOT update trades here to avoid infinite recursion
        # self.update_trades()

        # Calculate metrics
        total_trades = self.performance_metrics["total_trades"]
        winning_trades = self.performance_metrics["winning_trades"]

        if total_trades > 0:
            self.performance_metrics["win_rate"] = winning_trades / total_trades

        # Calculate average win/loss - safely handle trades that might not have profit attribute
        wins = []
        losses = []

        for t in self.trades:
            # Ensure we're working with TradeOrderV2 objects
            if isinstance(t, TradeOrderV2) and t.status == "closed":
                if t.profit > 0:
                    wins.append(t.profit)
                else:
                    losses.append(t.profit)

        if wins:
            self.performance_metrics["average_win"] = sum(wins) / len(wins)
        if losses:
            self.performance_metrics["average_loss"] = sum(losses) / len(losses)

        # Calculate profit factor
        if losses and sum(abs(l) for l in losses) > 0:
            self.performance_metrics["profit_factor"] = sum(wins) / sum(abs(l) for l in losses)

        # Calculate Sharpe ratio (simplified)
        if self.trades:
            returns = [t.profit for t in self.trades if isinstance(t, TradeOrderV2) and t.status == "closed"]
            if returns and len(returns) > 1 and np.std(returns) > 0:
                self.performance_metrics["sharpe_ratio"] = np.mean(returns) / np.std(returns)

        return self.performance_metrics

    def _manage_open_positions(self):
        """
        CRITICAL FIX: Manage open positions to ensure profitability.
        Close positions based on time, profit targets, or stop losses.
        """
        try:
            if not hasattr(self, 'mt5_connector') or not self.mt5_connector:
                return

            # Get current open positions
            positions = self.mt5_connector.get_open_positions()
            if not positions:
                return

            current_time = datetime.now()
            position_count = len(positions)

            # EMERGENCY: Close all positions if too many (prevent margin exhaustion)
            if position_count > 20:
                logger.warning(f"[{self.terminal_id}] EMERGENCY: {position_count} positions detected - closing all to prevent margin exhaustion")
                for position in positions:
                    try:
                        ticket = getattr(position, 'ticket', None)
                        if ticket:
                            current_profit = getattr(position, 'profit', 0.0)
                            close_result = self.mt5_connector.close_position(ticket)
                            if close_result:
                                logger.info(f"[{self.terminal_id}] EMERGENCY CLOSE: Position {ticket} closed with P&L: ${current_profit:.2f}")
                                # Update performance metrics
                                if current_profit > 0:
                                    self.performance_metrics["winning_trades"] += 1
                                    self.performance_metrics["total_profit"] += current_profit
                                else:
                                    self.performance_metrics["losing_trades"] += 1
                                    self.performance_metrics["total_loss"] += abs(current_profit)
                    except Exception as e:
                        logger.error(f"[{self.terminal_id}] Error in emergency close: {str(e)}")
                return

            # Normal position management
            for position in positions:
                try:
                    ticket = getattr(position, 'ticket', None)
                    if not ticket:
                        continue

                    # Get position details
                    open_time = datetime.fromtimestamp(getattr(position, 'time', 0))
                    current_profit = getattr(position, 'profit', 0.0)
                    # position_type = getattr(position, 'type', 0)  # 0=BUY, 1=SELL (not used)
                    # volume = getattr(position, 'volume', 0.0)  # (not used)

                    # Calculate position age in minutes
                    position_age = (current_time - open_time).total_seconds() / 60

                    should_close = False
                    close_reason = ""

                    # Rule 1: Close profitable positions after 15 minutes (REDUCED for faster turnover)
                    if current_profit > 2.0 and position_age > 15:
                        should_close = True
                        close_reason = f"Profit target reached: ${current_profit:.2f} after {position_age:.1f}min"

                    # Rule 2: Close losing positions after 30 minutes (REDUCED to prevent large losses)
                    elif current_profit < -5.0 and position_age > 30:
                        should_close = True
                        close_reason = f"Stop loss: ${current_profit:.2f} after {position_age:.1f}min"

                    # Rule 3: Close any position after 60 minutes (REDUCED to prevent accumulation)
                    elif position_age > 60:
                        should_close = True
                        close_reason = f"Time limit: {position_age:.1f}min (P&L: ${current_profit:.2f})"

                    # Rule 4: Close very profitable positions quickly (take profit)
                    elif current_profit > 10.0:
                        should_close = True
                        close_reason = f"Quick profit: ${current_profit:.2f}"

                    # Rule 5: Close positions older than 10 minutes with any profit
                    elif current_profit > 0.5 and position_age > 10:
                        should_close = True
                        close_reason = f"Small profit secured: ${current_profit:.2f} after {position_age:.1f}min"

                    if should_close:
                        logger.info(f"[{self.terminal_id}] Closing position {ticket}: {close_reason}")

                        # Close the position
                        close_result = self.mt5_connector.close_position(ticket)
                        if close_result:
                            logger.info(f"[{self.terminal_id}] Position {ticket} closed successfully with P&L: ${current_profit:.2f}")

                            # Update performance metrics
                            if current_profit > 0:
                                self.performance_metrics["winning_trades"] += 1
                                self.performance_metrics["total_profit"] += current_profit
                            else:
                                self.performance_metrics["losing_trades"] += 1
                                self.performance_metrics["total_loss"] += abs(current_profit)
                        else:
                            logger.warning(f"[{self.terminal_id}] Failed to close position {ticket}")

                except Exception as pos_error:
                    logger.error(f"[{self.terminal_id}] Error managing position: {str(pos_error)}")
                    continue

        except Exception as e:
            logger.error(f"[{self.terminal_id}] Error in position management: {str(e)}")

    def _check_position_limits(self):
        """
        CRITICAL FIX: Check position limits to prevent margin exhaustion.
        Disable trading if too many positions are open.
        """
        try:
            if not hasattr(self, 'mt5_connector') or not self.mt5_connector:
                return

            # Get current open positions
            positions = self.mt5_connector.get_open_positions()
            position_count = len(positions) if positions else 0

            # Set position limits
            max_positions = 10  # Maximum allowed positions per terminal
            warning_threshold = 8  # Warning threshold

            # Update trading status based on position count
            if position_count >= max_positions:
                self.trading_enabled = False
                logger.warning(f"[{self.terminal_id}] TRADING DISABLED: {position_count} positions (max: {max_positions})")
            elif position_count >= warning_threshold:
                self.trading_enabled = True  # Still allow trading but warn
                logger.warning(f"[{self.terminal_id}] HIGH POSITION COUNT: {position_count} positions (warning at: {warning_threshold})")
            else:
                self.trading_enabled = True
                if position_count > 0:
                    logger.info(f"[{self.terminal_id}] Position count: {position_count} (within limits)")

            # Store position count for monitoring
            if not hasattr(self, 'position_count'):
                self.position_count = 0
            self.position_count = position_count

        except Exception as e:
            logger.error(f"[{self.terminal_id}] Error checking position limits: {str(e)}")
            # Default to enabled if error occurs
            self.trading_enabled = True

    def run_iteration(self):
        """Run a single trading iteration."""
        try:
            # Connect to terminal
            if not self.mt5_connector.is_connected():
                if not self.connect():
                    logger.error(f"Terminal {self.terminal_id} failed to connect")
                    return False

            # Update trades
            self.update_trades()

            # CRITICAL FIX: Manage open positions for profitability
            self._manage_open_positions()

            # CRITICAL FIX: Check position limits before allowing new trades
            self._check_position_limits()

            # Generate trading signal
            signal = self.generate_trading_signal()

            # Execute trade if signal is valid
            if signal and hasattr(signal, 'action') and signal.action != 'hold':
                # Execute trade and store the result
                trade_result = self.execute_trade(signal)

                if trade_result:
                    logger.info(f"Terminal {self.terminal_id} executed {signal.action} order with ticket {trade_result}")

                    # The trade is already added to self.trades in execute_trade
                    # Find the trade in the trades list
                    for trade in self.trades:
                        if hasattr(trade, 'ticket') and trade.ticket == trade_result:
                            # Calculate performance metrics
                            self.calculate_performance_metrics()
                            return trade

                    # If trade not found in list (shouldn't happen), create a new TradeOrderV2
                    trade = TradeOrderV2(
                        ticket=trade_result,
                        action=getattr(signal, 'action', 'unknown'),
                        symbol=self.symbol,
                        price=getattr(signal, 'price', None),
                        stop_loss=getattr(signal, 'stop_loss', None),
                        take_profit=getattr(signal, 'take_profit', None),
                        volume=getattr(signal, 'volume', None),
                        timestamp=datetime.now(),
                        source_model=self.primary_model,
                        profit=0.0,
                        profit_loss=0.0,
                        status="open"
                    )
                    # Trade is already added to self.trades in execute_trade
                    # Calculate performance metrics
                    self.calculate_performance_metrics()
                    return trade
                else:
                    logger.warning(f"Terminal {self.terminal_id} failed to execute {signal.action} order")

            # Calculate performance metrics
            self.calculate_performance_metrics()

            return True

        except Exception as e:
            logger.error(f"Error in Terminal {self.terminal_id} iteration: {str(e)}")
            logger.debug(f"Exception details:", exc_info=True)
            return False


class TradingManager:
    """Manages multiple independent trading bots with proportional allocation."""

    def __init__(self):
        """Initialize the trading manager."""
        self.trading_bots = {}
        self.running = False
        self._lock = threading.RLock()
        self.initialize_bots()

    def initialize_bots(self):
        """Initialize trading bots with proportional allocation."""
        # Define terminal-model pairings with allocation percentages
        terminal_configs = [
            TerminalConfig(terminal_id=1, primary_model="lstm", allocation_percentage=35),
            TerminalConfig(terminal_id=3, primary_model="tft", allocation_percentage=35),
            TerminalConfig(terminal_id=5, primary_model="arima", allocation_percentage=30)
        ]

        # Create trading bot for each terminal
        for config in terminal_configs:
            bot = IndependentTradingBot(config)
            self.trading_bots[config.terminal_id] = bot

        logger.info(f"Initialized {len(self.trading_bots)} trading bots")

    def run_all_bots(self):
        """Run all trading bots in parallel."""
        # Use ThreadPoolExecutor to run bots in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            # Submit tasks
            futures = {executor.submit(bot.run_iteration): terminal_id
                      for terminal_id, bot in self.trading_bots.items()}

            # Process results
            for future in concurrent.futures.as_completed(futures):
                terminal_id = futures[future]
                try:
                    # Get the result (which can be a dictionary, TradeOrder object, or a boolean)
                    result = future.result()

                    # Debug: Check result type and attributes
                    logger.debug(f"Bot for terminal {terminal_id} returned result of type: {type(result)}")
                    if result is not None and not isinstance(result, bool):
                        logger.debug(f"Result attributes: {dir(result)}")

                    # Check if result is None or False (indicating failure)
                    if result is None or result is False:
                        logger.warning(f"Bot for terminal {terminal_id} failed to run iteration")
                    elif isinstance(result, dict) and 'ticket' in result:
                        # Handle dictionary result
                        logger.debug(f"Bot for terminal {terminal_id} executed trade with ticket {result['ticket']}")
                    elif isinstance(result, TradeOrderV2):
                        # Handle TradeOrder or TradeOrderV2 result
                        logger.debug(f"Bot for terminal {terminal_id} executed trade with ticket {result.ticket}")
                    else:
                        logger.debug(f"Bot for terminal {terminal_id} completed iteration successfully")

                except Exception as e:
                    logger.error(f"Error running bot for terminal {terminal_id}: {str(e)}")
                    logger.debug(f"Exception details for terminal {terminal_id}:", exc_info=True)

    def adjust_allocations(self):
        """Adjust allocation percentages based on performance."""
        with self._lock:
            # Get performance metrics for each bot
            performance_metrics = {}
            for terminal_id, bot in self.trading_bots.items():
                metrics = bot.calculate_performance_metrics()
                performance_metrics[terminal_id] = metrics

            # Calculate new allocations based on performance
            if performance_metrics:
                new_allocations = self._calculate_optimal_allocations(performance_metrics)

                # Update bot allocations
                for terminal_id, allocation in new_allocations.items():
                    if terminal_id in self.trading_bots:
                        self.trading_bots[terminal_id].allocation_percentage = allocation
                        logger.info(f"Updated allocation for terminal {terminal_id} to {allocation}%")

    def _calculate_optimal_allocations(self, performance_metrics):
        """Calculate optimal allocations based on performance metrics."""
        # Use Sharpe ratio as the primary metric for allocation
        sharpe_ratios = {}
        for terminal_id, metrics in performance_metrics.items():
            sharpe_ratio = metrics.get("sharpe_ratio", 0)
            # Ensure non-negative value
            sharpe_ratios[terminal_id] = max(0.1, sharpe_ratio)

        # Calculate allocations proportional to Sharpe ratios
        total_sharpe = sum(sharpe_ratios.values())
        if total_sharpe > 0:
            allocations = {terminal_id: (ratio / total_sharpe) * 100
                          for terminal_id, ratio in sharpe_ratios.items()}
        else:
            # Default allocations if no performance data
            allocations = {1: 35, 3: 35, 5: 30}

        return allocations

    def get_performance_summary(self):
        """Get performance summary for all bots."""
        summary = {}
        total_profit = 0

        for terminal_id, bot in self.trading_bots.items():
            metrics = bot.calculate_performance_metrics()
            summary[terminal_id] = {
                "primary_model": bot.primary_model,
                "allocation": bot.allocation_percentage,
                "total_trades": metrics["total_trades"],
                "win_rate": metrics["win_rate"],
                "profit": metrics["total_profit"],
                "sharpe_ratio": metrics["sharpe_ratio"]
            }
            total_profit += metrics["total_profit"]

        summary["total"] = {
            "total_profit": total_profit
        }

        return summary

    def start(self):
        """Start the trading manager."""
        with self._lock:
            if self.running:
                logger.warning("Trading manager is already running")
                return False

            self.running = True

            # Start the main thread
            self._thread = threading.Thread(target=self._run_loop)
            self._thread.daemon = True
            self._thread.start()

            logger.info("Trading manager started")
            return True

    def stop(self):
        """Stop the trading manager."""
        with self._lock:
            if not self.running:
                logger.warning("Trading manager is not running")
                return False

            self.running = False

            logger.info("Trading manager stopped")
            return True

    def _run_loop(self):
        """Main run loop for the trading manager."""
        iteration = 0

        try:
            while self.running:
                logger.info(f"Running iteration {iteration + 1}")

                try:
                    # Debug: Check trading bots before running
                    logger.debug(f"Trading bots before iteration {iteration + 1}: {list(self.trading_bots.keys())}")
                    for terminal_id, bot in self.trading_bots.items():
                        logger.debug(f"Bot {terminal_id} has type {type(bot)} and attributes {dir(bot)}")

                    # Run all bots with error handling
                    try:
                        self.run_all_bots()
                    except Exception as bot_e:
                        logger.error(f"Error running bots in iteration {iteration + 1}: {str(bot_e)}")
                        logger.debug(f"Exception details for bot execution in iteration {iteration + 1}:", exc_info=True)

                    # Adjust allocations every 24 hours (assuming 15-minute iterations)
                    if iteration % 96 == 0 and iteration > 0:  # 96 = 24 hours / 15 minutes
                        try:
                            self.adjust_allocations()
                        except Exception as alloc_e:
                            logger.error(f"Error adjusting allocations in iteration {iteration + 1}: {str(alloc_e)}")
                            logger.debug(f"Exception details for allocation adjustment in iteration {iteration + 1}:", exc_info=True)

                    # Log performance summary every hour
                    if iteration % 4 == 0:  # 4 = 1 hour / 15 minutes
                        try:
                            summary = self.get_performance_summary()
                            logger.info(f"Performance summary: {summary}")
                        except Exception as summary_e:
                            logger.error(f"Error getting performance summary in iteration {iteration + 1}: {str(summary_e)}")
                            logger.debug(f"Exception details for performance summary in iteration {iteration + 1}:", exc_info=True)
                except Exception as inner_e:
                    logger.error(f"Error in iteration {iteration + 1}: {str(inner_e)}")
                    logger.debug(f"Exception details for iteration {iteration + 1}:", exc_info=True)
                    # Continue running despite errors in a single iteration

                # Sleep between iterations (REDUCED FREQUENCY)
                time.sleep(900)  # 15 minutes (REDUCED from 5 minutes)

                iteration += 1

        except Exception as e:
            logger.error(f"Error in trading manager run loop: {str(e)}")
            logger.debug("Exception details for run loop:", exc_info=True)
            self.running = False


def main():
    """Main function to run the trading manager."""
    # Initialize trading manager
    manager = TradingManager()

    # Start visualization in a separate thread
    visualization_thread = threading.Thread(
        target=lambda: os.system('python visualize_training.py --log_file model_training.log --output_dir visualizations --monitor --interval 30'),
        daemon=True
    )
    visualization_thread.start()
    logger.info("Started visualization thread")

    try:
        # Start trading manager
        manager.start()

        # Keep main thread alive
        while True:
            try:
                time.sleep(1)
                # Print performance summary every 60 seconds
                if int(time.time()) % 60 == 0:
                    summary = manager.get_performance_summary()
                    logger.info(f"Performance summary: {summary}")
            except Exception as inner_e:
                logger.error(f"Error in main loop: {str(inner_e)}")
                # Continue running despite errors in the main loop

    except KeyboardInterrupt:
        logger.info("Trading manager stopped by user")
        manager.stop()

    except Exception as e:
        logger.error(f"Error in main function: {str(e)}")
        manager.stop()


if __name__ == "__main__":
    main()
